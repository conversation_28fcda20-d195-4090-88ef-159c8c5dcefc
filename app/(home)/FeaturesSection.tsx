'use client'

import { motion } from 'framer-motion';
import lang from '@/common/lang';

const getFeatureIcon = (iconType: string) => {
  switch (iconType) {
  case 'platforms':
    return (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-7H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V6a2 2 0 00-2-2zM9 7h6m-6 4h6m-7 4h8" />
    );
  case 'ai':
    return (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
    );
  case 'schedule':
    return (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
    );
  case 'image':
    return (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
    );
  case 'knowledge':
    return (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
    );
  case 'analytics':
    return (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    );
  default:
    return (
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
    );
  }
};

export const FeaturesSection = () => {
  const { clientPage } = lang;

  return (
    <motion.section
      initial={{
        opacity: 0,
        y: 20,
      }}
      animate={{
        opacity: 1,
        y: 0,
      }}
      transition={{
        duration: 0.5,
        delay: 0.6,
      }}
      className="mb-24"
      id="features"
    >
      <div className="text-center mb-12">
        <h2 className="text-2xl md:text-4xl font-bold text-white mb-4">
          {clientPage.features.title}
        </h2>
        <p className="text-lg text-gray-300 max-w-2xl mx-auto">
          {clientPage.features.description}
        </p>
      </div>

      <div className="mb-12">
        <div className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6 md:p-8 hover:border-violets-are-blue/20 transition-all duration-300">
          <div className="flex flex-col md:flex-row items-center gap-8">
            <div className="flex-1">
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-tr from-han-purple to-tulip flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
              </div>
              <h3 className="text-2xl font-semibold text-white mb-4">{clientPage.features.mainFeature.title}</h3>
              <p className="text-gray-300 text-lg mb-6">{clientPage.features.mainFeature.description}</p>
            </div>
            <div className="flex-1">
              <div className="bg-violets-are-blue/10 rounded-2xl p-6 border border-white/5">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gradient-to-tr from-han-purple/50 to-tulip/50 flex items-center justify-center">
                    <span className="text-white text-sm">💡</span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400 mb-2">Example:</p>
                    <p className="text-gray-300">{clientPage.features.mainFeature.example}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {clientPage.features.coreFeatures.map((feature, index) => (
          <motion.div
            key={index}
            initial={{ 
              opacity: 0, 
              y: 20,
            }}
            animate={{ 
              opacity: 1, 
              y: 0,
            }}
            transition={{ 
              duration: 0.5, 
              delay: 0.8 + index * 0.1,
            }}
            className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6 hover:border-violets-are-blue/20 transition-all duration-300 group"
          >
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-tr from-han-purple to-tulip flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {getFeatureIcon(feature.icon)}
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-3">{feature.title}</h3>
            <p className="text-gray-300">{feature.description}</p>
          </motion.div>
        ))}
      </div>
    </motion.section>
  );
};
