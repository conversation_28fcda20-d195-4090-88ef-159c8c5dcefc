'use client'

import { motion } from 'framer-motion';
import lang from '@/common/lang';
import { 
  XIcon, 
  LinkedInColorIcon, 
  InstagramIcon, 
  FacebookIcon, 
  PinterestIcon, 
  YoutubeIcon,
} from '@/common/components/icons';

const platformIcons = {
  'Twitter/X': <XIcon className="w-8 h-8" />,
  'LinkedIn': <LinkedInColorIcon className="w-8 h-8" />,
  'Instagram': <InstagramIcon className="w-8 h-8" />,
  'Facebook': <FacebookIcon className="w-8 h-8" />,
  'Pinterest': <PinterestIcon className="w-8 h-8" />,
  'YouTube': <YoutubeIcon className="w-8 h-8" />,
};

const platformColors = {
  'Twitter/X': 'from-gray-600 to-black',
  'LinkedIn': 'from-blue-600 to-blue-800',
  'Instagram': 'from-pink-500 to-purple-600',
  'Facebook': 'from-blue-500 to-blue-700',
  'Pinterest': 'from-red-500 to-red-700',
  'YouTube': 'from-red-600 to-red-800',
};

export const PlatformsSection = () => {
  const { clientPage } = lang;

  return (
    <motion.section
      initial={{
        opacity: 0,
        y: 20,
      }}
      animate={{
        opacity: 1,
        y: 0,
      }}
      transition={{
        duration: 0.5,
        delay: 1.0,
      }}
      className="mb-24"
      id="platforms"
    >
      <div className="text-center mb-12">
        <h2 className="text-2xl md:text-4xl font-bold text-white mb-4">
          {clientPage.platforms.title}
        </h2>
        <p className="text-lg text-gray-300 max-w-2xl mx-auto">
          {clientPage.platforms.description}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {clientPage.platforms.supportedPlatforms.map((platform, index) => (
          <motion.div
            key={platform.name}
            initial={{ 
              opacity: 0, 
              y: 20,
            }}
            animate={{ 
              opacity: 1, 
              y: 0,
            }}
            transition={{ 
              duration: 0.5, 
              delay: 1.2 + index * 0.1,
            }}
            className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6 hover:border-violets-are-blue/20 transition-all duration-300 group"
          >
            <div className="flex items-center gap-4 mb-4">
              <div className={`w-12 h-12 rounded-2xl bg-gradient-to-tr ${platformColors[platform.name as keyof typeof platformColors]} flex items-center justify-center transition-transform duration-300`}>
                {platformIcons[platform.name as keyof typeof platformIcons]}
              </div>
              <h3 className="text-xl font-semibold text-white">{platform.name}</h3>
            </div>
            
            <p className="text-gray-300 mb-4">{platform.description}</p>
            
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-400 mb-2">Key Features:</p>
              <div className="grid grid-cols-2 gap-2">
                {platform.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-han-purple to-tulip"></div>
                    <span className="text-sm text-gray-300 -mt-1.5">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.section>
  );
};
