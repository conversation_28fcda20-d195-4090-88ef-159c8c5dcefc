'use client'

import { motion } from 'framer-motion';
import {
  <PERSON><PERSON>, <PERSON>,
} from '@/common/components/atoms';
import { Integrations } from '@/common/components/molecules';
import lang from '@/common/lang';
import { useSupabaseAuth } from '@/common/hooks';
import { useRouter } from 'next/navigation';
import { routes } from '@/common/routes';

interface HeroSectionProps {
  handleGetStarted: () => void;
}

export const HeroSection = ({ handleGetStarted }: HeroSectionProps) => {
  const { clientPage } = lang;
  const { isAuthenticated } = useSupabaseAuth();
  const router = useRouter();

  return (
    <section className="mt-8 md:mt-0 mb-20 md:mb-28" id="hero">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-8 md:gap-12">
        <motion.div
          initial={{
            opacity: 0,
            y: 20,
          }}
          animate={{
            opacity: 1,
            y: 0,
          }}
          transition={{
            duration: 0.5,
          }}
          className="flex-1 text-center md:text-left"
        >
          <div className="inline-block mb-4 px-4 py-1.5 rounded-full bg-violets-are-blue/10 border border-violets-are-blue/20">
            <p className="text-sm font-medium text-white">{clientPage.hero.badge}</p>
          </div>

          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
            <span className="block">{clientPage.hero.title.line1}</span>
            <span className="bg-gradient-to-r from-han-purple to-tulip bg-clip-text text-transparent">{clientPage.hero.title.highlight}</span>
            <span className="block">{clientPage.hero.title.line2}</span>
          </h1>

          <p className="text-xl text-gray-300 max-w-2xl md:max-w-lg mb-8">
            {clientPage.hero.description}
          </p>

          <div className="grid grid-cols-3 gap-4 mb-8">
            {clientPage.hero.stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ 
                  opacity: 0, 
                  y: 20,
                }}
                animate={{ 
                  opacity: 1, 
                  y: 0,
                }}
                transition={{ 
                  duration: 0.5, 
                  delay: 0.3 + index * 0.1,
                }}
                className="text-center md:text-left"
              >
                <div className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-han-purple to-tulip bg-clip-text text-transparent">
                  {stat.value}
                </div>
                <div className="text-sm font-medium text-white">{stat.label}</div>
                <div className="text-xs text-gray-400">{stat.description}</div>
              </motion.div>
            ))}
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
            <Button
              size="lg"
              variant="gradient"
              onClick={isAuthenticated ? () => router.push(routes.dashboardPath) : handleGetStarted}
              className="px-8"
            >
              {isAuthenticated ? clientPage.hero.buttons.goToDashboard : clientPage.hero.buttons.signUp}
            </Button>
            <Link
              variant="outline"
              size="lg"
              href="#how-it-works"
              className="px-8"
            >
              {clientPage.hero.buttons.learnMore}
            </Link>
          </div>
        </motion.div>

        <motion.div
          initial={{
            opacity: 0,
            scale: 0.95,
          }}
          animate={{
            opacity: 1,
            scale: 1,
          }}
          transition={{
            duration: 0.5,
            delay: 0.2,
          }}
          className="flex-1 relative hidden md:block"
        >
          <div className="relative w-full h-[300px] max-w-sm mx-auto">
            <div className="absolute inset-0 bg-gradient-to-tr from-han-purple/20 to-tulip/20 rounded-3xl border border-white/10 backdrop-blur-sm flex items-center justify-center overflow-hidden p-4">
              <Integrations />
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};
