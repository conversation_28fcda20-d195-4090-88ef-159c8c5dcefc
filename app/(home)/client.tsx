'use client'
import {
  useState, useEffect,
} from 'react';
import { AuthModal } from '@/common/components/organisms';
import { Toaster } from '@/common/components/molecules';
import {
  useSupabaseAuth, useResetPasswordToken,
} from '@/common/hooks';
import { useRouter } from 'next/navigation';
import { routes } from '@/common/routes';
import {
  ClientLayout,
  CTASection,
  FAQSection,
  FeaturesSection,
  HeroSection,
  HowItWorksSection,
  PlatformsSection,
  PricingSection,
} from '.';

export const Client = () => {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const { isAuthenticated } = useSupabaseAuth();
  const {
    resetToken, shouldOpenAuthModal, clearResetToken,
  } = useResetPasswordToken();
  const router = useRouter();

  useEffect(() => {
    if (shouldOpenAuthModal) {
      setIsAuthModalOpen(true);
    }
  }, [shouldOpenAuthModal]);

  const handleGetStarted = () => {
    if (isAuthenticated) {
      router.push(routes.dashboardPath);
    } else {
      setIsAuthModalOpen(true);
    }
  };

  return (
    <ClientLayout>
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => {
          setIsAuthModalOpen(false);
          clearResetToken();
        }}
        resetToken={resetToken}
        initialTab="signup"
      />
      <HeroSection handleGetStarted={handleGetStarted} />
      <HowItWorksSection />
      <FeaturesSection />
      <PlatformsSection />
      {!isAuthenticated && (
        <>
          <PricingSection handleGetStarted={handleGetStarted} />
          <FAQSection />
          <CTASection handleGetStarted={handleGetStarted} />
        </>
      )}
      {isAuthenticated && (
        <FAQSection />
      )}
      <Toaster />
    </ClientLayout>
  );
}
