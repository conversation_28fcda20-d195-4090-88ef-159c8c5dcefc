'use client'

import { motion } from 'framer-motion';
import lang from '@/common/lang';

export const HowItWorksSection = () => {
  const { clientPage } = lang;

  return (
    <motion.section
      initial={{
        opacity: 0,
        y: 20,
      }}
      animate={{
        opacity: 1,
        y: 0,
      }}
      transition={{
        duration: 0.5,
        delay: 0.4,
      }}
      className="mb-24"
      id="how-it-works"
    >
      <div className="text-center mb-12">
        <h2 className="text-2xl md:text-4xl font-bold text-white mb-4">
          {clientPage.howItWorks.title}
        </h2>
        <p className="text-lg text-gray-300 max-w-2xl mx-auto">
          {clientPage.howItWorks.description}
        </p>
      </div>

      <div className="hidden md:grid grid-cols-2 gap-6">
        {clientPage.howItWorks.steps.map((step, index) => (
          <div key={index} className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6 hover:border-violets-are-blue/20 transition-all duration-300 flex flex-col">
            <div className="mb-4 flex items-center">
              <div className="w-10 h-10 rounded-full bg-gradient-to-tr from-han-purple to-tulip flex items-center justify-center text-white font-bold mr-3 aspect-square">{index + 1}</div>
              <h3 className="text-lg font-semibold text-white">{step.title}</h3>
            </div>
            <p className="text-gray-300 mb-4 flex-grow">{step.description}</p>
          </div>
        ))}
      </div>

      <div className="md:hidden space-y-6">
        {clientPage.howItWorks.steps.map((step, index) => (
          <div key={index} className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6 hover:border-violets-are-blue/20 transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 rounded-full bg-gradient-to-tr from-han-purple to-tulip flex items-center justify-center text-white font-bold mr-4 aspect-square">{index + 1}</div>
              <h3 className="text-xl font-semibold text-white">{step.title}</h3>
            </div>
            <p className="text-gray-300 mb-4">{step.description}</p>
          </div>
        ))}
      </div>
    </motion.section>
  );
};
