import type { SVGProps } from "react";
interface IconProps extends SVGProps<SVGSVGElement> {
  width?: number | string;
  height?: number | string;
}
export const PinterestIcon = ({
  width = 24,
  height = 24,
  ...props
}: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    fill="none"
    viewBox="0 0 24 24"
    {...props}
  >
    <path
      fill="#FF808C"
      stroke="#191919"
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M12.513 1C6.511 1 3.49 5.303 3.49 8.89c0 2.174.824 4.113 2.587 4.827a.435.435 0 0 0 .632-.316c.06-.22.198-.78.26-1.014a.612.612 0 0 0-.183-.704 3.634 3.634 0 0 1-.833-2.477 5.983 5.983 0 0 1 6.215-6.05c3.393 0 5.26 2.072 5.26 4.84 0 3.642-1.614 6.717-4.007 6.717a1.955 1.955 0 0 1-1.994-2.43c.383-1.601 1.116-3.326 1.116-4.481a1.692 1.692 0 0 0-1.704-1.897c-1.351 0-2.436 1.397-2.436 3.27a4.851 4.851 0 0 0 .403 1.998l-1.626 6.885a14.153 14.153 0 0 0-.038 4.8.168.168 0 0 0 .3.075 13.522 13.522 0 0 0 2.281-4.136c.155-.563.888-3.473.888-3.473a3.62 3.62 0 0 0 3.086 1.574c4.06-.004 6.814-3.706 6.814-8.66C20.51 4.49 17.337 1 12.513 1Z"
    />
  </svg>
)
