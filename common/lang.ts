const header = {
  login: 'Login',
  signUp: 'Sign Up',
  pilotSubheading: "Enhance your social presence",
}

const resetPassword = {
  title: 'Reset Your Password',
  newPassword: 'New Password',
  confirmPassword: 'Confirm Password',
  resetButton: 'Reset Password',
  updatingButton: 'Updating...',
  passwordRequirements: 'Password must be at least 6 characters with lowercase, uppercase letters and numbers',
  successMessage: 'Password updated successfully!',
  errorMessage: 'Error updating password',
  invalidToken: 'Invalid or missing reset token',
}

const notFound = {
  heading: 'Something went wrong',
  subHeading1: 'Brace yourself till we get the error fixed',
  subHeading2: 'You may also refresh the page or try again later',
  buttonTitle: 'Return Home',
}

const footer = {
  terms: 'Terms',
  aboutUs: 'About Us',
  privacyPolicy: 'Privacy',
  contactUs: 'Contact us',
  contactUsModal: {
    heading: 'Contact Us',
    submitButton: 'Send',
    youCanContact: 'You can contact us at',
  },
}


const homePage = {
  // Empty object - keeping structure but removing unused keys
}

const manageIdea = {
  // Only keeping the promptLoadingStates which is used in constants.ts
  promptLoadingStates: [
    {
      text: "Crafting Your Digital Identity",
    },
    {
      text: "Preparing Your Agent",
    },
    {
      text: "Processing Platform Data",
    },
    {
      text: "Designing Engagement Plan",
    },
    {
      text: "Optimizing Post Schedule",
    },
  ],
  imageUpload: {
    postImage: "Upload Image",
    uploadLabel: "Click to upload",
    imageSizeError: "Image size should be less than 5MB",
    imageType: "Only JPG, PNG, GIF, SVG, WEBP, APNG, and AVIF formats are supported",
    uploadError: "Failed to upload image. Please try again.",
  },
}

const clientPage = {
  hero: {
    badge: 'Introducing smart socials',
    title: {
      line1: 'Meet your',
      highlight: 'AI Marketing Team',
      line2: 'Streamline your Socials',
    },
    description: 'Combine AI-powered content creation (with your touch!), multi-platform scheduling, and automated engagement strategies to grow your business or brand.',
    stats: [
      {
        value: '80%',
        label: 'Time Saved',
        description: 'Reduce social media management time',
      },
      {
        value: '6+',
        label: 'Platforms',
        description: 'Twitter, LinkedIn, Instagram & more',
      },
      {
        value: '24/7',
        label: 'AI Assistant',
        description: 'Always working for your brand',
      },
    ],
    buttons: {
      signUp: 'Start Free Trial',
      goToDashboard: 'Go to Dashboard',
      learnMore: 'See How It Works',
    },
  },
  howItWorks: {
    title: 'Simple. Seamless. Smart.',
    description: 'Discover how Media Pilot transforms your ideas into powerful outreach in four easy steps',
    steps: [
      {
        title: 'Share your ideas',
        description: 'Share your content ideas on our user-friendly Content Calendar',
      },
      {
        title: 'AI Magic',
        description: 'MediaPilot analyzes your idea, accesses your knowledge base, and prepares high quality, optimized content for each platform.',
      },
      {
        title: 'Review & Approve',
        description: 'Review the generated content, make any necessary edits, add images or videos and approve for scheduling or immediate posting.',
      },
      {
        title: 'Continuous Learning',
        description: 'Our AI learns from posted content and engagement metrics to continuously improve your content strategy and performance.',
      },
    ],
  },
  features: {
    title: 'Professional Marketing that works while you sleep 💤',
    description: 'The Marketing AI that thinks like you, posts like you, and engages like you—to find the ideal customer for your vision.',
    mainFeature: {
      title: 'AI Co-Creator for Content',
      description: 'Brainstorm, draft, and refine your social media posts with your AI partner. Get instant suggestions for improving tone, clarity, and engagement, ensuring your content always hits the mark.',
      example: 'Post announcing Dreamathon, our nationwide hackathon series in India',
    },
    coreFeatures: [
      {
        title: 'Multi-Platform Publishing',
        description: 'Seamlessly publish to Twitter/X, LinkedIn, Instagram, Facebook, Pinterest, and YouTube from a single dashboard',
        icon: 'platforms',
      },
      {
        title: 'AI Content Generation',
        description: 'Advanced AI creates platform-optimized content using multiple models including GPT-4, Claude, and Llama',
        icon: 'ai',
      },
      {
        title: 'Smart Scheduling',
        description: 'Intelligent scheduling system that posts at optimal times for maximum engagement across all platforms',
        icon: 'schedule',
      },
      {
        title: 'AI Image Generation',
        description: 'Create stunning visuals with AI-powered image generation using DALL-E, Midjourney, and other leading models',
        icon: 'image',
      },
      {
        title: 'Knowledge Base Integration',
        description: 'Upload documents, PDFs, and links to train your AI on your brand voice and company information',
        icon: 'knowledge',
      },
      {
        title: 'Analytics & Insights',
        description: 'Comprehensive analytics to track performance, engagement, and ROI across all your social media channels',
        icon: 'analytics',
      },
    ],
    otherFeatures: [
      {
        title: 'Perfect Optimization',
        description: 'Your content is modified and optimized for the target platform with zero additional effort',
      },
      {
        title: "World's First EngAgent",
        description: 'Engage, comment on and follow audiences most suited to your vision',
      },
      {
        title: '24/7 Customer Support (for free)',
        description: 'Accurate, authentic replies and support to your customer queries online - automagically',
      },
      {
        title: 'Intelligent Continuous Learning',
        description: 'Continuous Learning system that is always improving itself based on analytics and engagement metrics',
      },
    ],
  },
  platforms: {
    title: 'Connect All Your Social Media Platforms',
    description: 'Manage your entire social media presence from one powerful dashboard',
    supportedPlatforms: [
      {
        name: 'Twitter/X',
        description: 'Full Twitter integration with advanced posting, engagement, and bot capabilities',
        features: ['Tweet scheduling', 'Thread creation', 'Auto-engagement', 'Analytics'],
      },
      {
        name: 'LinkedIn',
        description: 'Professional networking with personal and business account support',
        features: ['Post scheduling', 'Organization posting', 'Professional content', 'Network growth'],
      },
      {
        name: 'Instagram',
        description: 'Visual content management with image and story support',
        features: ['Photo posting', 'Story creation', 'Hashtag optimization', 'Visual analytics'],
      },
      {
        name: 'Facebook',
        description: 'Comprehensive Facebook page and profile management',
        features: ['Page posting', 'Event promotion', 'Community engagement', 'Ad integration'],
      },
      {
        name: 'Pinterest',
        description: 'Visual discovery platform for creative content sharing',
        features: ['Pin creation', 'Board management', 'SEO optimization', 'Traffic driving'],
      },
      {
        name: 'YouTube',
        description: 'Video content management and community engagement',
        features: ['Video uploads', 'Community posts', 'Comment management', 'Analytics'],
      },
    ],
  },
  services: {
    title: 'Comprehensive Social Media Management',
    description: 'Everything you need to build and grow your social media presence',
    serviceList: [
      {
        title: 'AI Content Creation',
        description: 'Generate high-quality, platform-specific content using advanced AI models',
        features: ['Multi-model AI support', 'Brand voice training', 'Platform optimization', 'Content variations'],
        icon: 'content',
      },
      {
        title: 'Visual Content Generation',
        description: 'Create stunning images and graphics with AI-powered design tools',
        features: ['AI image generation', 'Custom graphics', 'Brand consistency', 'Multiple formats'],
        icon: 'visual',
      },
      {
        title: 'Smart Scheduling',
        description: 'Optimize posting times and manage your content calendar efficiently',
        features: ['Optimal timing', 'Bulk scheduling', 'Calendar view', 'Time zone support'],
        icon: 'calendar',
      },
      {
        title: 'Performance Analytics',
        description: 'Track engagement, growth, and ROI across all your social media channels',
        features: ['Real-time metrics', 'Growth tracking', 'ROI analysis', 'Custom reports'],
        icon: 'chart',
      },
      {
        title: 'Knowledge Management',
        description: 'Train your AI with your brand documents and content guidelines',
        features: ['Document upload', 'Brand voice training', 'Content guidelines', 'Knowledge base'],
        icon: 'brain',
      },
      {
        title: 'Automated Engagement',
        description: 'AI-powered engagement to grow your audience and build relationships',
        features: ['Auto-responses', 'Audience targeting', 'Engagement optimization', 'Community building'],
        icon: 'engagement',
      },
    ],
  },
  pricing: {
    title: 'Pricing that scales with you',
    description: 'Whichever plan you pick, it\'s free until you love your content. That\'s our promise.',
    billingOptions: {
      monthly: 'Monthly',
      yearly: 'Yearly',
      discount: '-20%',
    },
    plans: [
      {
        name: 'Free',
        price: '$0',
        period: '/month',
        description: 'Perfect for individual users',
        buttonText: 'Start Free',
        features: [
          'Connect up to 3 social accounts',
          'Basic AI content generation',
          'Up to 15 posts per month',
          'Basic analytics',
        ],
      },
      {
        name: 'Startup',
        price: '$12',
        period: '/month',
        description: 'Ideal for professionals and small teams',
        buttonText: 'Upgrade to Pro',
        popular: 'Popular',
        features: [
          'Everything in Free +',
          'Connect up to 10 social accounts',
          'Advanced AI content generation',
          'Unlimited posts',
          'Advanced analytics',
          'Custom scheduling',
        ],
      },
      {
        name: 'Enterprise',
        price: '$24',
        period: '/month',
        description: 'Best for large teams and enterprise-level organizations',
        buttonText: 'Contact Sales',
        features: [
          'Everything in Pro +',
          'Unlimited social accounts',
          'Priority AI content generation',
          'Advanced team collaboration',
          'Dedicated account manager',
        ],
      },
    ],
  },
  faq: {
    title: 'Frequently Asked Questions',
    description: 'Answers to common questions about Media Pilot and its features. If you have any other questions, please don\'t hesitate to contact us.',
    questions: [
      {
        question: 'What is MediaPilot?',
        answer: 'MediaPilot is your AI Marketing Team that creates authentic content, engages with your audience, and grows your following across X, LinkedIn, Instagram, Facebook, Lens & Farcaster—24/7. Think of it as hiring a marketing team without the salary, equity, or management overhead.',
      },
      {
        question: 'How does Media Pilot work?',
        answer: 'Upload your company docs, past content, and brand guidelines. Our AI learns your voice and starts creating platform-optimized posts that sound exactly like you. It also engages with relevant accounts, responds to comments, and continuously improves based on what performs best. You review and approve—or let it run on full autopilot.',
      },
      {
        question: 'How secure is my data?',
        answer: 'We use industry-standard encryption and security practices to protect your data. Your content and account information are never shared with third parties without your explicit permission.',
      },
      {
        question: 'Can I integrate my existing tools?',
        answer: 'Yes—connects with your CRM, analytics tools, email platforms, and project management software. We built it to fit into your existing workflow, not replace it. Plus, our API lets your developers customize integrations as you scale.',
      },
      {
        question: 'Is there a free trial available?',
        answer: 'Yes, we offer a 14 day free trial with no credit card required. You can explore all the features of our platform before deciding on a subscription plan.',
      },
      {
        question: 'How does Media Pilot save me time?',
        answer: 'By automating content creation, scheduling, and cross-platform posting, Media Pilot reduces the time you spend on social media management by up to 80%, allowing you to focus on strategy and growth.',
      },
    ],
  },
  cta: {
    title: 'Automate. Simplify. Thrive.',
    description: 'Start your 14 day free trial today and let us build your audience, while you build your product.',
    buttonText: 'Start Free Trial',
  },
};

const paymentSuccess = {
  title: 'Payment Successful!',
  processing: 'Processing your payment...',
  activated: 'Your project has been activated successfully.',
  activationIssue: 'We received your payment, but there was an issue activating your project. Please contact support.',
  errorRetrieving: 'Failed to retrieve payment details',
  errorProcessing: 'An error occurred while processing your payment',
  errorUpdating: 'Failed to update project status',
  successToast: 'Payment successful! Your project is now active.',
  buttonText: 'Go to Dashboard',
};

const paymentError = {
  title: 'Payment Failed',
  defaultMessage: 'There was an issue processing your payment. Your card has not been charged.',
  errorToast: 'Payment processing failed',
  buttonText: 'Go to Dashboard',
};

export const lang = {
  header,
  manageIdea,
  homePage,
  footer,
  notFound,
  clientPage,
  resetPassword,
  paymentSuccess,
  paymentError,
};

export default lang;
